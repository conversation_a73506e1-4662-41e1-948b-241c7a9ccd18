"use client";
import { z } from "zod";
import type { FormType } from "@/types";
import toast from "react-hot-toast";
import { ArrowRight } from "lucide-react";
import { formatFieldId } from "@/lib/utils";
import { zodResolver } from "@hookform/resolvers/zod";
import type { UseFormRegister, UseFormSetValue, UseFormWatch } from "react-hook-form";
import { useForm } from "react-hook-form";
import { stegaClean } from "next-sanity";
import { Button } from "../ui/button";
import { Input } from "../ui/input";
import { Checkbox } from "../ui/checkbox";
import { RadioGroup, RadioGroupItem } from "../ui/radio-group";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select";
import { useEffect } from "react";
import { useSearchParams } from "next/navigation";

export default function Form({ form }: { form: FormType }) {
  const { fields, submitButtonText } = form;
  const searchParams = useSearchParams();

  const formSchema = z.object(
    fields?.reduce((acc, field) => {
      const fieldName = stegaClean(field?.name) ?? "";
      let validator: z.ZodTypeAny;

      // Handle different field types
      switch (field.inputType) {
        case "email":
          validator = z.string().email("Invalid email address");
          break;
        case "file":
          validator = z.any(); // File inputs need special handling
          break;
        case "checkbox":
          validator = z.union([z.string(), z.array(z.string())]);
          break;
        case "date":
          validator = z.string().refine((val) => !val || !isNaN(Date.parse(val)), {
            message: "Invalid date format",
          });
          break;
        case "time":
          validator = z.string().refine((val) => !val || /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.test(val), {
            message: "Invalid time format (HH:MM)",
          });
          break;
        default:
          validator = z.string();
      }

      // Apply required validation
      if (field.isRequired) {
        if (field.inputType === "checkbox") {
          validator = validator.refine((val) => {
            if (Array.isArray(val)) return val.length > 0;
            return val && val.length > 0;
          }, `${stegaClean(field.name)} is required`);
        } else {
          validator = validator.min(1, `${stegaClean(field.name)} is required`);
        }
      }

      return { ...acc, [fieldName]: validator };
    }, {}) ?? {},
  );

  type FormValues = z.infer<typeof formSchema>;

  const {
    register,
    handleSubmit,
    reset,
    setValue,
    watch,
    formState: { errors },
  } = useForm<FormValues>({
    resolver: zodResolver(formSchema),
  });

  // Set default values from URL parameters
  useEffect(() => {
    if (fields) {
      fields.forEach((field) => {
        const fieldName = stegaClean(field?.name) ?? "";
        const urlParam = field.urlParameter;

        if (urlParam && searchParams.has(urlParam)) {
          const value = searchParams.get(urlParam);
          if (value) {
            setValue(fieldName as keyof FormValues, value as any);
          }
        }
      });
    }
  }, [fields, searchParams, setValue]);

  const onSubmit = async (data: FormValues) => {
    try {
      console.log(data);
      const response = await fetch("/api/send", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          formId: form._id,
          formTitle: form.title,
          fields: data,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to send message");
      }

      await response.json();
      reset();
      toast.success("Message Sent");
    } catch (error) {
      toast.error((error as Error)?.message as string);
    }
  };

  return (
    <form
      onSubmit={handleSubmit(onSubmit)}
      className="flex flex-col w-full max-w-xl p-6 md:p-8 space-y-6 border backdrop-blur-xs rounded-xl md:rounded-3xl"
    >
      {fields?.map((field) => (
        <div key={field.name} className="space-y-2">
          <label
            htmlFor={formatFieldId(field.name ?? "")}
            className="text-sm font-medium"
          >
            {field.name}{" "}
            {field.isRequired && <span className="text-red-500">*</span>}
          </label>
          <FieldRenderer
            field={field}
            register={register}
            setValue={setValue}
            watch={watch}
          />
          {errors[field.name as keyof typeof errors] && (
            <p className="text-sm text-red-500">
              {errors[field.name as keyof typeof errors]?.message as string}
            </p>
          )}
        </div>
      ))}
      <Button variant="primary" type="submit" className="ml-auto">
        <span className="font-medium text-sm">{submitButtonText}</span>{" "}
        <ArrowRight
          size={16}
          className="group-hover:translate-x-1 transition-transform duration-300"
        />
      </Button>
    </form>
  );
}

function FieldRenderer({
  field,
  register,
  setValue,
  watch,
}: {
  field: NonNullable<FormType["fields"]>[number];
  register: UseFormRegister<Record<string, any>>;
  setValue: UseFormSetValue<Record<string, any>>;
  watch: UseFormWatch<Record<string, any>>;
}) {
  const fieldName = stegaClean(field.name ?? "");
  const fieldId = formatFieldId(field.name ?? "");
  const placeholder = stegaClean(field.placeholder);
  const options = field.options || [];

  switch (field.inputType) {
    case "text":
    case "email":
    case "tel":
      return (
        <Input
          id={fieldId}
          {...register(fieldName)}
          type={field.inputType}
          placeholder={placeholder}
        />
      );

    case "textarea":
      return (
        <textarea
          id={fieldId}
          {...register(fieldName)}
          placeholder={placeholder}
          rows={4}
          className="w-full px-4 py-2 border rounded-lg bg-card resize-none"
        />
      );

    case "date":
      return (
        <Input
          id={fieldId}
          {...register(fieldName)}
          type="date"
          placeholder={placeholder}
        />
      );

    case "time":
      return (
        <Input
          id={fieldId}
          {...register(fieldName)}
          type="time"
          placeholder={placeholder}
        />
      );

    case "file":
      return (
        <Input
          id={fieldId}
          {...register(fieldName)}
          type="file"
          className="file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-primary file:text-primary-foreground hover:file:bg-primary/80"
        />
      );

    case "select":
      return (
        <Select
          onValueChange={(value) => setValue(fieldName, value)}
          defaultValue={watch(fieldName)}
        >
          <SelectTrigger id={fieldId} className="w-full">
            <SelectValue placeholder={placeholder || "Select an option"} />
          </SelectTrigger>
          <SelectContent>
            {options.map((option, index) => (
              <SelectItem key={index} value={stegaClean(option.value) || ""}>
                {stegaClean(option.label)}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      );

    case "radio":
      return (
        <RadioGroup
          onValueChange={(value) => setValue(fieldName, value)}
          defaultValue={watch(fieldName)}
          className="flex flex-col space-y-2"
        >
          {options.map((option, index) => (
            <div key={index} className="flex items-center space-x-2">
              <RadioGroupItem
                value={stegaClean(option.value) || ""}
                id={`${fieldId}-${index}`}
              />
              <label
                htmlFor={`${fieldId}-${index}`}
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                {stegaClean(option.label)}
              </label>
            </div>
          ))}
        </RadioGroup>
      );

    case "checkbox":
      if (options.length > 1) {
        // Multiple checkboxes
        return (
          <div className="flex flex-col space-y-2">
            {options.map((option, index) => {
              const optionValue = stegaClean(option.value) || "";
              const currentValues = watch(fieldName) || [];
              const isChecked = Array.isArray(currentValues)
                ? currentValues.includes(optionValue)
                : currentValues === optionValue;

              return (
                <div key={index} className="flex items-center space-x-2">
                  <Checkbox
                    id={`${fieldId}-${index}`}
                    checked={isChecked}
                    onCheckedChange={(checked) => {
                      const current = watch(fieldName) || [];
                      const currentArray = Array.isArray(current) ? current : [];

                      if (checked) {
                        setValue(fieldName, [...currentArray, optionValue]);
                      } else {
                        setValue(fieldName, currentArray.filter((v: string) => v !== optionValue));
                      }
                    }}
                  />
                  <label
                    htmlFor={`${fieldId}-${index}`}
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    {stegaClean(option.label)}
                  </label>
                </div>
              );
            })}
          </div>
        );
      } else {
        // Single checkbox
        const option = options[0];
        return (
          <div className="flex items-center space-x-2">
            <Checkbox
              id={fieldId}
              checked={!!watch(fieldName)}
              onCheckedChange={(checked) => {
                setValue(fieldName, checked ? (option?.value || "true") : "");
              }}
            />
            <label
              htmlFor={fieldId}
              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              {option ? stegaClean(option.label) : placeholder}
            </label>
          </div>
        );
      }

    case "custom":
      // For custom fields, render a placeholder that can be replaced with custom logic
      return (
        <div
          id={fieldId}
          className="w-full px-4 py-2 border rounded-md bg-muted text-muted-foreground text-center"
          data-custom-field={(field as any)._key}
        >
          Custom field implementation needed (Key: {(field as any)._key})
        </div>
      );

    default:
      return null;
  }
}
