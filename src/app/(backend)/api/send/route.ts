import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest, response: NextResponse) {
  const body = await request.json();
  try {
    console.log(body);

    const payload = {
      formId: body.formId,
      fields: body.fields,
      metadata: {
        ip: request.headers.get("x-forwarded-for"),
        userAgent: request.headers.get("user-agent"),
        url: request.url,
        timestamp: new Date().toISOString(),
      },
    };
    const url = "https://nocobase.autocorner.ch/api/dynamic_forms:create";
    const options = {
      method: "POST",
      headers: {
        Authorization:
          "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTcwODYzNDksImV4cCI6MzMzMTQ2ODYzNDl9.AS-kc2lLEwyfUdeRwmmHXZfpTIexCrBxwvEO0P77IGA",
        "Content-Type": "application/json",
      },
      body: JSON.stringify(payload),
    };
    const response = await fetch(url, options);
    const data = await response.json();
    console.log(data);
    return Response.json({ message: "Message sent successfully" });
  } catch (error) {
    console.error(error);
    return Response.json({ error }, { status: 500 });
  }
}
