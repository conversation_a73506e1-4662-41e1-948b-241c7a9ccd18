import { Send } from "lucide-react";
import { defineField, defineType } from "sanity";
import { fieldsets } from "../../misc/fieldsets";
import { fieldGroups } from "../../misc/field-groups";
import { formBlockDict } from "../../../dictionary/studio/schemas/page-builder/blocks/form-block";

const { fields, validation } = formBlockDict;

export default defineType({
  name: "formBlock",
  title: fields.subtitle,
  type: "object",
  fieldsets: [...fieldsets],
  groups: [...fieldGroups],
  fields: [
    defineField({
      name: "heading",
      title: fields.heading,
      type: "string",
    }),
    defineField({
      name: "content",
      title: fields.content,
      type: "array",
      of: [
        {
          type: "block",
          styles: [{ title: "Normal", value: "normal" }],
          lists: [],
        },
      ],
    }),
    defineField({
      name: "form",
      title: fields.form,
      type: "reference",
      to: [{ type: "form" }],
      validation: (Rule) => Rule.required().error(validation.formRequired),
    }),
    defineField({
      name: "anchorId",
      title: fields.anchorId,
      type: "string",
    }),
  ],
  preview: {
    select: {
      title: "heading",
      media: "",
    },
    prepare(selection) {
      const { title } = selection;
      return {
        title: title ?? fields.noTitle,
        subtitle: fields.subtitle,
        media: Send,
      };
    },
  },
});
