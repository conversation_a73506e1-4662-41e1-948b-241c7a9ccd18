import { File } from "lucide-react";
import { fieldsets } from "../misc/fieldsets";
import { defineField, defineType } from "sanity";
import { fieldGroups } from "../misc/field-groups";
import { formDict } from "../../dictionary/studio/schemas/documents/form";

// Extract constants for cleaner code
const { document, fields, validation, descriptions } = formDict;

export default defineType({
  name: "form",
  title: document.title,
  type: "document",
  icon: File,
  fieldsets: [...fieldsets],
  groups: [...fieldGroups],
  fields: [
    defineField({
      name: "title",
      title: fields.formTitle,
      type: "string",
      validation: (rule) => rule.required().error(validation.titleRequired),
    }),
    defineField({
      name: "submitButtonText",
      title: fields.submitButtonText,
      type: "string",
      validation: (rule) =>
        rule.required().error(validation.submitButtonTextRequired),
    }),
    define<PERSON>ield({
      name: "fields",
      title: fields.formFields,
      type: "array",
      of: [
        {
          type: "object",
          fields: [
            defineField({
              name: "name",
              title: fields.name,
              type: "string",
              description: descriptions.name,
            }),
            defineField({
              name: "placeholder",
              title: fields.placeholder,
              type: "string",
              description: descriptions.placeholder,
            }),
            defineField({
              name: "inputType",
              title: fields.inputType,
              type: "string",
              description: descriptions.inputType,
              options: {
                list: [
                  { title: fields.text, value: "text" },
                  { title: fields.textarea, value: "textarea" },
                  { title: fields.email, value: "email" },
                  { title: fields.telephone, value: "tel" },
                  { title: fields.checkbox, value: "checkbox" },
                  { title: fields.radio, value: "radio" },
                  { title: fields.select, value: "select" },
                  { title: fields.file, value: "file" },
                  { title: fields.date, value: "date" },
                  { title: fields.time, value: "time" },
                  { title: fields.custom, value: "custom" },
                ],
              },
              initialValue: "text",
            }),
            defineField({
              name: "isRequired",
              title: fields.isRequired,
              type: "boolean",
              description: descriptions.isRequired,
              initialValue: false,
            }),
            defineField({
              name: "urlParameter",
              title: fields.urlParameter,
              type: "string",
              description: descriptions.urlParameter,
            }),
            defineField({
              name: "options",
              title: fields.options,
              type: "array",
              description: descriptions.options,
              hidden: ({ parent }) => !["select", "radio", "checkbox"].includes(parent?.inputType),
              of: [
                {
                  type: "object",
                  fields: [
                    defineField({
                      name: "label",
                      title: fields.optionLabel,
                      type: "string",
                      description: descriptions.optionLabel,
                    }),
                    defineField({
                      name: "value",
                      title: fields.optionValue,
                      type: "string",
                      description: descriptions.optionValue,
                    }),
                  ],
                  preview: {
                    select: {
                      title: "label",
                      subtitle: "value",
                    },
                  },
                },
              ],
            }),
          ],
          preview: {
            select: {
              title: "name",
              subtitle: "inputType",
            },
            prepare({ title, subtitle }) {
              return {
                title: title || "Unnamed field",
                subtitle: subtitle || "text",
              };
            },
          },
        },
      ],
    }),
  ],
});
