import { defineField, defineType } from "sanity";
import { fieldsets } from "../../misc/fieldsets";
import { fieldGroups } from "../../misc/field-groups";
import { buttonFields } from "../../misc/button-fields";
import { linkFields } from "../../misc/link-fields";
import { brandsPageDict } from "../../../dictionary/studio/schemas/singletons/pages/brands-page";

const { document, fields, descriptions } = brandsPageDict;

const Tab = [
  defineField({
    name: "label",
    title: "Label",
    type: "string",
  }),
  defineField({
    name: "description",
    title: "Description",
    type: "string",
  }),
];

export default defineType({
  name: document.name,
  title: document.title,
  type: "document",
  fieldsets: [...fieldsets],
  groups: [...fieldGroups],
  fields: [
    defineField({
      name: "title",
      title: fields.title,
      type: "string",
      description: descriptions.title,
    }),
    defineField({
      name: "slug",
      title: fields.slug,
      type: "slug",
      options: {
        source: "title",
        maxLength: 96,
      },
      description: descriptions.slug,
    }),
    defineField({
      name: "pageBuilder",
      title: fields.pageBuilder,
      type: "pageBuilder",
      description: descriptions.pageBuilder,
    }),
    defineField({
      name: "seo",
      title: fields.seo,
      type: "seoObject",
      description: descriptions.seo,
    }),
  ],
});
