import { fieldsets } from "../misc/fieldsets";
import { defineField, defineType } from "sanity";
import { fieldGroups } from "../misc/field-groups";
import { brandsSettingsDict } from "../../dictionary/studio/schemas/settings/brands";
import { linkFields } from "../misc/link-fields";
import { buttonFields } from "../misc/button-fields";

const { document, fields, descriptions } = brandsSettingsDict;

const Tab = [
  defineField({
    name: "label",
    title: "Label",
    type: "string",
  }),
  defineField({
    name: "description",
    title: "Description",
    type: "string",
  }),
];

export default defineType({
  name: "brandsSettings",
  title: document.title,
  type: "document",
  fieldsets: [...fieldsets],
  groups: [...fieldGroups],
  fields: [
    // Brand Grid States Object (SIMPLE LABELS)
    defineField({
      name: "brandGridStates",
      title: fields.brandGridStates,
      type: "object",
      group: "translations",
      description: descriptions.brandGridStates,
      fields: [
        {
          name: "noBrandsFound",
          title: fields.noBrandsFound,
          type: "string",
          initialValue: fields.noBrandsFound,
          description: descriptions.noBrandsFound,
        },
        {
          name: "checkBackLater",
          title: fields.checkBackLater,
          type: "string",
          initialValue: fields.checkBackLater,
          description: descriptions.checkBackLater,
        },
        {
          name: "noVehiclesFound",
          title: fields.noVehiclesFound,
          type: "string",
          initialValue: fields.noVehiclesFound,
          description: descriptions.noVehiclesFound,
        },
        {
          name: "checkBackForModels",
          title: fields.checkBackForModels,
          type: "string",
          initialValue: fields.checkBackForModels,
          description: descriptions.checkBackForModels,
        },
        {
          name: "noBrandsAvailable",
          title: fields.noBrandsAvailable,
          type: "string",
          initialValue: fields.noBrandsAvailable,
          description: descriptions.noBrandsAvailable,
        },
      ],
      options: {
        collapsible: true,
        collapsed: true,
      },
    }),

    // Vehicle Model Content Object (SIMPLE LABELS)
    defineField({
      name: "vehicleModelContent",
      title: fields.vehicleModelContent,
      type: "object",
      group: "translations",
      description: descriptions.vehicleModelContent,
      fields: [
        {
          name: "interestedIn",
          title: fields.interestedIn,
          type: "string",
          initialValue: fields.interestedIn,
          description: descriptions.interestedIn,
        },
        {
          name: "specialistsCanHelp",
          title: fields.specialistsCanHelp,
          type: "string",
          initialValue: fields.specialistsCanHelp,
          description: descriptions.specialistsCanHelp,
        },
        {
          name: "testDrive",
          title: fields.testDrive,
          type: "string",
          initialValue: fields.testDrive,
          description: descriptions.testDrive,
        },
        {
          name: "contact",
          title: fields.contact,
          type: "string",
          initialValue: fields.contact,
          description: descriptions.contact,
        },
        {
          name: "logoAltText",
          title: fields.logoAltText,
          type: "string",
          initialValue: fields.logoAltText,
          description: descriptions.logoAltText,
        },
        {
          name: "imageAltText",
          title: fields.imageAltText,
          type: "string",
          initialValue: fields.imageAltText,
          description: descriptions.imageAltText,
        },
      ],
      options: {
        collapsible: true,
        collapsed: true,
      },
    }),

    // Brand Quick Actions (MIXED: linkFields + buttonFields)
    defineField({
      name: "brandQuickActions",
      title: fields.brandQuickActions,
      type: "object",
      group: "shortcuts",
      description: descriptions.brandQuickActions,
      fields: [
        // Collapsible section actions (Link components)
        defineField({
          name: "bookTestDriveAction",
          title: fields.bookTestDriveAction,
          type: "object",
          description: descriptions.bookTestDriveAction,
          fields: linkFields,
        }),
        defineField({
          name: "findDealerAction",
          title: fields.findDealerAction,
          type: "object",
          description: descriptions.findDealerAction,
          fields: linkFields,
        }),
        defineField({
          name: "contactSalesAction",
          title: fields.contactSalesAction,
          type: "object",
          description: descriptions.contactSalesAction,
          fields: linkFields,
        }),
        defineField({
          name: "getBrochureAction",
          title: fields.getBrochureAction,
          type: "object",
          description: descriptions.getBrochureAction,
          fields: linkFields,
        }),
      ],
      options: {
        collapsible: true,
        collapsed: true,
      },
    }),

    // Vehicle Model Tabs Object (SIMPLE LABELS)
    defineField({
      name: "vehicleModelTabs",
      title: fields.vehicleModelTabs,
      type: "object",
      group: "tabs",
      description: descriptions.vehicleModelTabs,
      fields: [
        {
          name: "overviewTab",
          title: fields.overviewTab,
          type: "object",
          fields: Tab,
          initialValue: {
            label: fields.overviewTab,
            description: fields.overviewTabDescription,
          },
          description: descriptions.overviewTab,
        },
        {
          name: "techSpecificationTab",
          title: fields.techSpecificationTab,
          type: "object",
          fields: Tab,
          initialValue: {
            label: fields.techSpecificationTab,
            description: fields.techSpecificationTabDescription,
          },
          description: descriptions.techSpecificationTab,
        },
        {
          name: "vehiclesTab",
          title: fields.vehiclesTab,
          type: "object",
          fields: Tab,
          initialValue: {
            label: fields.vehiclesTab,
            description: fields.vehiclesTabDescription,
          },
          description: descriptions.vehiclesTab,
        },
        {
          name: "galleryTab",
          title: fields.galleryTab,
          type: "object",
          fields: Tab,
          initialValue: {
            label: fields.galleryTab,
            description: fields.galleryTabDescription,
          },
          description: descriptions.galleryTab,
        },
        {
          name: "offersTab",
          title: fields.offersTab,
          type: "object",
          fields: Tab,
          initialValue: {
            label: fields.offersTab,
            description: fields.offersTabDescription,
          },
          description: descriptions.offersTab,
        },
        {
          name: "configureTab",
          title: fields.configureTab,
          type: "object",
          fields: Tab,
          initialValue: {
            label: fields.configureTab,
            description: fields.configureTabDescription,
          },
          description: descriptions.configureTab,
        },
      ],
      options: {
        collapsible: true,
        collapsed: true,
      },
    }),

    // Vehicle Model Navigation (linkFields)
    defineField({
      name: "vehicleModelNavigation",
      title: fields.vehicleModelNavigation,
      type: "object",
      group: "navigation",
      description: descriptions.vehicleModelNavigation,
      fields: [
        defineField({
          name: "overviewNavigation",
          title: fields.overviewNavigation,
          type: "object",
          description: descriptions.overviewNavigation,
          fields: linkFields,
        }),
        defineField({
          name: "galleryNavigation",
          title: fields.galleryNavigation,
          type: "object",
          description: descriptions.galleryNavigation,
          fields: linkFields,
        }),
        defineField({
          name: "configureNavigation",
          title: fields.configureNavigation,
          type: "object",
          description: descriptions.configureNavigation,
          fields: linkFields,
        }),
        defineField({
          name: "compareNavigation",
          title: fields.compareNavigation,
          type: "object",
          description: descriptions.compareNavigation,
          fields: linkFields,
        }),
      ],
      options: {
        collapsible: true,
        collapsed: true,
      },
    }),

    // Vehicle Model Quick Actions (MIXED: linkFields + buttonFields)
    defineField({
      name: "vehicleModelQuickActions",
      title: fields.vehicleModelQuickActions,
      type: "object",
      group: "shortcuts",
      description: descriptions.vehicleModelQuickActions,
      fields: [
        // Collapsible section actions (Link components)
        defineField({
          name: "bookTestDriveModelAction",
          title: fields.bookTestDriveModelAction,
          type: "object",
          description: descriptions.bookTestDriveModelAction,
          fields: linkFields,
        }),
        defineField({
          name: "configureModelAction",
          title: fields.configureModelAction,
          type: "object",
          description: descriptions.configureModelAction,
          fields: linkFields,
        }),
        defineField({
          name: "compareModelAction",
          title: fields.compareModelAction,
          type: "object",
          description: descriptions.compareModelAction,
          fields: linkFields,
        }),
        defineField({
          name: "findDealerModelAction",
          title: fields.findDealerModelAction,
          type: "object",
          description: descriptions.findDealerModelAction,
          fields: linkFields,
        }),
        defineField({
          name: "contactSalesModelAction",
          title: fields.contactSalesModelAction,
          type: "object",
          description: descriptions.contactSalesModelAction,
          fields: linkFields,
        }),
        defineField({
          name: "shareVehicleAction",
          title: fields.shareVehicleAction,
          type: "object",
          description: descriptions.shareVehicleAction,
          fields: linkFields,
        }),
      ],
      options: {
        collapsible: true,
        collapsed: true,
      },
    }),

    defineField({
      name: "vehicleModelCTA",
      title: fields.vehicleModelCTA,
      type: "object",
      group: "cta",
      description: descriptions.vehicleModelCTA,
      fields: [
        defineField({
          name: "title",
          title: "Title",
          type: "string",
        }),
        defineField({
          name: "description",
          title: "Description",
          type: "text",
          rows: 3,
        }),
        // CTA buttons (Button components)
        defineField({
          name: "testDriveModelButton",
          title: fields.testDriveModelButton,
          type: "object",
          description: descriptions.testDriveModelButton,
          fields: buttonFields,
        }),
        defineField({
          name: "contactModelButton",
          title: fields.contactModelButton,
          type: "object",
          description: descriptions.contactModelButton,
          fields: buttonFields,
        }),
      ],
      options: {
        collapsible: true,
        collapsed: true,
      },
    }),
  ],
});
