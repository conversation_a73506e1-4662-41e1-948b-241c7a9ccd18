import {
  baseQuery,
  buttonQuery,
  dataSourceQuery,
  mediaQuery,
  paddingQuery,
} from "../misc";

export const vehicleListBlockQuery = `
  _type == "vehicleListBlock" => {
    ${baseQuery},
    title,
    subtitle,
    ctaButton {
      text,
      url,
      style
    },
    layout,
    cardVariant,
    itemsPerView,
    itemsPerRow,
    aspectRatio,
    spacing,
    preFilters {
      centerId,
      makeKey,
      modelKey,
      fuelType,
      conditionType,
      priceFrom,
      priceTo
    },
    enabledFilters,
    filterLayout,
    theme,
    brandIntegration
  }
`;

export const vehicleSearchToolbarFieldsQuery = `
  ${baseQuery},
  enableCenterFilter,
  defaultCenterId,
  availableFilters,
  popularFilters[] {
    label,
    filters {
      centerId,
      makeKey,
      modelKey,
      fuelType,
      conditionType,
      priceFrom,
      priceTo
    }
  },
  previewLayout,
  previewItemsPerView,
  targetRoute,
  showVehicleCount,
  autoUpdateCount
`;

export const vehicleSearchToolbarQuery = `
  _type == "vehicleSearchToolbar" => {
    ${vehicleSearchToolbarFieldsQuery}
  }
`;

export const mediaBlockQuery = `
  _type == "mediaBlock" => {
    ${baseQuery},
    backgroundType,
    backgroundWidth,
    image { 
      ${mediaQuery}
    },
    muxVideo {
      video {
        asset->{
          _id,
          assetId,
          data,
          status,
          playbackId,
          filename
        }
      },
      title,
      description,
      poster { 
        ${mediaQuery}
      },
      controls,
      autoplay,
      loop,
      muted,
      aspectRatio,
      maxResolution,
      captions,
      language
    },
    overlayType,
    dialogType,
    videoUrl,
    anchorId
  }
`;

export const heroBlockQuery = `
  _type == "heroBlock" => {
    ${baseQuery},
    heading,
    content[]{
      _type == "block" => {
        ...
      },
      _type == "callToActionObject" => {
        ...,
        buttons[]{
          ${buttonQuery}
        }
      }
    },
    blockType,
    media {
     ${mediaBlockQuery}
    },
    disableVideoOnMobile,
    bottomCornerRadius,
    buttons[]{
      ${buttonQuery}
    },
    image {
      height,
      ${mediaQuery}
    },
    layout,
    blockOrder,
    dialogType,
    videoUrl,
    overlayType,
    enableVehicleSearch,
    vehicleSearchToolbar {
      ${vehicleSearchToolbarFieldsQuery}
    },
    anchorId
  }
`;

export const featureBlockQuery = `
  _type == "featureBlock" => {
    ${baseQuery},
    heading,
    features[] {
      title,
      description,
      icon { 
        ${mediaQuery}
      },
      pageReference->{
        _id,
        title,
        "slug": slug.current
      }
    },
    anchorId
  }
`;

export const featureCardsBlockQuery = `
  _type == "featureCardsBlock" => {
    ${baseQuery},
    heading,
    buttons[]{
      ${buttonQuery}
    },
    features[] {
      _key,
      title,
      description,
      items,
      image { 
        ${mediaQuery}
      },
      button {
        ${buttonQuery}
      },  
    },
    showCallToAction,
    callToActionHeading,
    callToActionContent,
    callToActionButtons[] {
      ${buttonQuery}
    },
    anchorId,
    ${paddingQuery}
  }
`;

export const featuresMinimalBlockQuery = `
  _type == "featuresMinimalBlock" => {
    ${baseQuery},
    heading,
    content,
    buttons[] {
      ${buttonQuery}
    },
    features,
    enableBorderTop,
    cornerRadiusTop,
    enableBorderBottom,
    cornerRadiusBottom,
    anchorId,
    ${paddingQuery}
  }
`;

export const callToActionBlockQuery = `
  _type == "callToActionBlock" => {
    ${baseQuery},
    heading,
    content,
    buttons[]{
      ${buttonQuery}
    },
    anchorId,
    ${paddingQuery}
  }
`;

export const logoBlockQuery = `
  _type == "logoBlock" => {
    ${baseQuery},
    heading,
    logos[] {
      _key,
      title,
      image { 
        ${mediaQuery}
      },
      size,
      link
    },
    anchorId
  }
`;
export const testimonialBlockQuery = `
  _type == "testimonialBlock" => {
    ${baseQuery},
    heading,
    eyebrow,
    testimonials[]->{
      _id,
      name,
      jobTitle,
      company,
      quote,
      avatar { 
        ${mediaQuery}
      },
      logo { 
        ${mediaQuery}
      },
    },
    anchorId,
    cornerRadiusTop,
    cornerRadiusBottom,
    ${paddingQuery}
  }
`;

export const freeformBlockQuery = `
  _type == "freeformBlock" => {
    ${baseQuery},
    title,
    columnsPerRow,
    columns[] {
      _key,
      _type,
      title,
      spacing,
      alignment,
      items[] {
        _key,
        _type,
        _type == "buttonObject" => {
          ${buttonQuery}
        },
        _type == "spacerObject" => {
          spacing
        },
        _type == "headingObject" => {
          heading,
          headingText,
          headingTag,
          headingSize
        },
        _type == "richTextObject" => {
          richTextContent
        },
        _type == "singleImageObject" => {
          image { 
            aspectRatio,
            ${mediaQuery}
          }
        }
      },
    },
    anchorId,
    border
  }
`;

export const portableTextBlockQuery = `
  _type == "portableTextBlock" => {
    ${baseQuery},
    title,
    content[],
    alignment,
    anchorId,
    ${paddingQuery}
  }
`;

export const blogArchiveBlockQuery = `
  _type == "blogArchiveBlock" => {
    ${baseQuery},
    heading,
    "categories": *[_type == "postCategory"] {
      _id,
      title,
      "slug": slug.current,
    },
    anchorId,
    ${paddingQuery}
  }
`;

export const servicesBlockQuery = `
  _type == "servicesBlock" => {
    ${baseQuery},
    heading,
    services[]->{
      _id,
      title,
      shortDescription,
      image { 
        ${mediaQuery}
      },
      "slug": slug.current,
    },
    buttons[]{
      ${buttonQuery}
    },
    background,
    topCornerRadius,
    anchorId,
    ${paddingQuery}
  }
`;

export const formBlockQuery = `
  _type == "formBlock" => {
    ${baseQuery},
    heading,
    content[],
    form->{
      ${baseQuery},
      title,
      submitButtonText,
      fields
    },
    anchorId,
    ${paddingQuery}
  }
`;

export const contentGridsBlockQuery = `
  _type == "contentGridsBlock" => {
    ${baseQuery},
    title,
    subtitle,
    ${dataSourceQuery}
    manualItems[] {
      _key,
      _type,
      title,
      description,
      image { 
        ${mediaQuery}
      },
      content[],
      link,
      category,
      tags,
      publishedAt,
      author,
      metadata
    },
    gridConfig {
      columns {
        mobile,
        tablet,
        desktop
      },
      spacing,
      aspectRatio,
      alignment
    },
    filterConfig {
      enableFilters,
      availableFilters,
      defaultFilter,
      enableSearch,
      enableSorting,
      sortOptions,
      enableCategoryFilter,
      enableTagFilter,
      enableDateFilter
    },
    enableLoadMore,
    initialItemCount,
    backgroundColor,
    imageFit,
    anchorId,
    ${paddingQuery}
  }
`;

export const processTimelinesBlockQuery = `
  _type == "processTimelinesBlock" => {
    ${baseQuery},
    title,
    subtitle,
    description,
    steps[] {
      _key,
      title,
      status,
      isMilestone,
      duration,
      content[] {
        _key,
        _type,
        _type == "buttonObject" => {
          ${buttonQuery}
        },
        image {
          ${mediaQuery}
        },
        heading,
        headingText,
        headingTag,
        headingSize,
        richTextContent,
        spacing
      }
    },
    timelineConfig {
      layout,
      orientation,
      showConnectors,
      connectorStyle,
      milestoneStyle
    },
    showOverallProgress,
    accentColor,
    backgroundColor,
    anchorId
  }
`;

export const statisticsBlockQuery = `
  _type == "statisticsBlock" => {
    ${baseQuery},
    title,
    description,
    statistics[] {
      _key,
      label,
      description,
      dataSource,
      numberFormatting {
        prefix,
        suffix,
        decimalPlaces,
        useThousandsSeparator,
        abbreviateLargeNumbers
      },
      animation,
      icon {
        iconType,
        lucideIcon,
        customImage { 
          ${mediaQuery}
        },
        emoji,
        position
      },
      styling {
        numberColor,
        labelColor,
        numberSize,
        fontWeight
      }
    },
    layout {
      columns {
        mobile,
        tablet,
        desktop
      },
      spacing,
      alignment
    },
    globalAnimation {
      enableStagger,
      staggerDelay
    },
    backgroundColor,
    anchorId
  }
`;

export const carouselBlockQuery = `
  _type == "carouselBlock" => {
    ${baseQuery},
    title,
    description,
    carouselConfig {
      slidesToShow {
        mobile,
        tablet,
        desktop
      },
      emblaOptions {
        align,
        loop,
        dragFree,
        slidesToScroll
      },
      slidesDisplay {
        slideSpacing,
        slidesPerView {
          mobile,
          tablet,
          desktop
        }
      },
      autoplay {
        enabled,
        delay,
        stopOnInteraction
      },
      orientation,
      showDots,
      showNavigation,
      showThumbnails,
      transitionEffect {
        type,
        duration
      }
    },
    contentType,
    ${dataSourceQuery}
    slides[] {
      _key,
      _type,
      slideType,
      image {
        ${mediaQuery}
        alt,
        caption
      },
      video {
        videoFile {
          asset->{
            _id,
            url,
            originalFilename,
            mimeType
          }
        },
        thumbnail { 
          ${mediaQuery}
        },
        caption
      },
      content[],
      title,
      description,
      button {
        text,
        url,
        style
      }
    },
    anchorId
  }
`;

export const headerBlockQuery = `
  _type == "headerBlock" => {
    ${baseQuery},
    heading,
    content[]{
      _type == "block" => {
        ...
      },
      _type == "callToActionObject" => {
        ...,
        buttons[]{
          ${buttonQuery}
        }
      },
      _type == "freeformBlock" => {
        ${freeformBlockQuery}
      }
    },
    bottomCornerRadius,
    anchorId
  }
`;

export const gridLayoutBlockQuery = `
  _type == "gridLayoutBlock" => {
    ${baseQuery},
    title,
    displayTitle,
    publicTitle,
    description,
    gridConfig,
    gridStyling,
    gridItems[] {
      _key,    
      ${baseQuery},
      gridItemConfig,
      block[]{
        ${heroBlockQuery},
        ${headerBlockQuery},
        ${featureBlockQuery},
        ${featureCardsBlockQuery},
        ${featuresMinimalBlockQuery},
        ${callToActionBlockQuery},
        ${logoBlockQuery},
        ${testimonialBlockQuery},
        ${freeformBlockQuery},
        ${portableTextBlockQuery},
        ${blogArchiveBlockQuery},
        ${servicesBlockQuery},
        ${formBlockQuery},
        ${mediaBlockQuery},
        ${contentGridsBlockQuery},
        ${processTimelinesBlockQuery},
        ${statisticsBlockQuery},
        ${carouselBlockQuery}
      }
    },
    anchorId
  }
`;
