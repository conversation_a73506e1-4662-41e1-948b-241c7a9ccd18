// English translations for form schema
export const formTranslations = {
  document: {
    name: "form",
    title: "Form",
  },
  fields: {
    formTitle: "Form Title",
    submitButtonText: "Submit Button Text",
    formFields: "Form Fields",
    name: "Name",
    placeholder: "Placeholder",
    inputType: "Input Type",
    isRequired: "Required",
    urlParameter: "URL Parameter",
    options: "Options",

    // Input types
    text: "Text",
    textarea: "Text Area",
    email: "Email",
    telephone: "Telephone",
    checkbox: "Checkbox",
    radio: "Radio",
    select: "Select",
    file: "File",
    date: "Date",
    time: "Time",
    custom: "Custom",

    // Option fields
    optionLabel: "Label",
    optionValue: "Value",
  },
  validation: {
    titleRequired: "Title is required",
    submitButtonTextRequired: "Submit button text is required",
  },
  descriptions: {
    formTitle: "The main title for this form",
    submitButtonText: "Text for the submit button",
    formFields: "Fields included in this form",
    name: "Field name",
    placeholder: "Placeholder text for the field",
    inputType: "Type of input (text, textarea, telephone, etc.)",
    isRequired: "Is this field required?",
    urlParameter: "URL parameter name to get default value from (e.g., 'email' to get value from ?email=<EMAIL>)",
    options: "Options for select, radio, and checkbox fields",

    // Input type descriptions
    text: "Single line text input",
    textarea: "Multi-line text area",
    email: "Email input with validation",
    telephone: "Telephone input",
    checkbox: "Checkbox input",
    radio: "Radio button group",
    select: "Dropdown select",
    file: "File upload",
    date: "Date picker",
    time: "Time picker",
    custom: "Custom field implementation",

    // Option descriptions
    optionLabel: "Display text for this option",
    optionValue: "Value sent when this option is selected",
  },
} as const;
