// French translations for form schema
export const formTranslations = {
  document: {
    name: "form",
    title: "Formulaire",
  },
  fields: {
    formTitle: "Titre du formulaire",
    submitButtonText: "Texte du bouton d'envoi",
    formFields: "Champs du formulaire",
    name: "Nom",
    placeholder: "Placeholder",
    inputType: "Type d'entrée",
    isRequired: "Obligatoire",
    urlParameter: "Paramètre URL",
    options: "Options",

    // Input types
    text: "Texte",
    textarea: "Zone de texte",
    email: "E-mail",
    telephone: "Téléphone",
    checkbox: "Case à cocher",
    radio: "Bouton radio",
    select: "Liste déroulante",
    file: "Fichier",
    date: "Date",
    time: "Heure",
    custom: "Personnalisé",

    // Option fields
    optionLabel: "Libellé",
    optionValue: "Valeur",
  },
  validation: {
    titleRequired: "Le titre est requis",
    submitButtonTextRequired: "Le texte du bouton d'envoi est requis",
  },
  descriptions: {
    formTitle: "Le titre principal de ce formulaire",
    submitButtonText: "Texte du bouton d'envoi",
    formFields: "Champs inclus dans ce formulaire",
    name: "Nom du champ",
    placeholder: "Texte d'espace réservé pour le champ",
    inputType: "Type d'entrée (texte, zone de texte, téléphone, etc.)",
    isRequired: "Ce champ est-il obligatoire ?",
    urlParameter: "Nom du paramètre URL pour obtenir la valeur par défaut (ex: 'email' pour obtenir la valeur de ?email=<EMAIL>)",
    options: "Options pour les champs select, radio et checkbox",

    // Input type descriptions
    text: "Champ de texte sur une ligne",
    textarea: "Zone de texte multi-lignes",
    email: "Champ e-mail avec validation",
    telephone: "Champ de saisie de téléphone",
    checkbox: "Case à cocher",
    radio: "Groupe de boutons radio",
    select: "Liste déroulante",
    file: "Téléchargement de fichier",
    date: "Sélecteur de date",
    time: "Sélecteur d'heure",
    custom: "Implémentation de champ personnalisé",

    // Option descriptions
    optionLabel: "Texte affiché pour cette option",
    optionValue: "Valeur envoyée lorsque cette option est sélectionnée",
  },
} as const;
