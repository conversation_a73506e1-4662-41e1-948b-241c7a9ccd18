// French translations for marketing settings schema
export const brandsSettingsTranslations = {
  document: {
    name: "marketingSettings",
    title: "Paramètres marketing",
    description: "Paramètres marketing et SEO",
  },
  fields: {
    // Brand Grid States Group (SIMPLE LABELS)
    brandGridStates: "États de grille de marques",
    noBrandsFound: "Aucune marque trouvée",
    checkBackLater: "Revenez plus tard pour de nouvelles marques.",
    noVehiclesFound: "Aucun véhicule trouvé",
    checkBackForModels: "Revenez plus tard pour de nouveaux modèles.",
    noBrandsAvailable: "Aucune marque disponible",

    // Brand Quick Actions Group (MIXED: linkFields + buttonFields)
    brandQuickActions: "Actions rapides de marque",
    bookTestDriveAction: "Action réserver un essai",
    findDealerAction: "Action trouver un concessionnaire",
    contactSalesAction: "Action contacter les ventes",
    getBrochureAction: "Action obtenir la brochure",

    // Vehicle Model Content Group (SIMPLE LABELS)
    vehicleModelContent: "Contenu modèle de véhicule",
    details: "DÉTAILS",
    interestedIn: "Intéressé par ce",
    specialistsCanHelp:
      "spécialistes peuvent vous aider à configurer, financer et programmer un essai routier.",
    testDrive: "Essai routier",
    contact: "Contact",
    logoAltText: "logo",
    imageAltText: "image",

    // Vehicle Model Tabs Group (SIMPLE LABELS)
    vehicleModelTabs: "Onglets modèle de véhicule",
    overviewTab: "APERÇU",
    overviewTabDescription: "Détails du véhicule",
    techSpecificationTab: "SPÉCIFICATIONS TECHNIQUES",
    techSpecificationTabDescription: "Détails techniques",
    vehiclesTab: "VÉHICULES",
    vehiclesTabDescription: "Modèles disponibles",
    galleryTab: "GALERIE",
    galleryTabDescription: "Photos & vidéos",
    offersTab: "OFFRES",
    offersTabDescription: "Offres spéciales et promotions",
    configureTab: "CONFIGURER",
    configureTabDescription: "Configurer & prix",

    // Vehicle Model Navigation Group (linkFields)
    vehicleModelNavigation: "Navigation modèle de véhicule",
    overviewNavigation: "Navigation aperçu",
    galleryNavigation: "Navigation galerie",
    configureNavigation: "Navigation configurer",
    compareNavigation: "Navigation comparer",

    // Vehicle Model Quick Actions Group (MIXED: linkFields + buttonFields)
    vehicleModelQuickActions: "Actions rapides modèle de véhicule",
    bookTestDriveModelAction: "Action réserver essai modèle",
    configureModelAction: "Action configurer modèle",
    compareModelAction: "Action comparer modèle",
    findDealerModelAction: "Action trouver concessionnaire modèle",
    contactSalesModelAction: "Action contacter ventes modèle",
    shareVehicleAction: "Action partager véhicule",
    testDriveModelButton: "Bouton essai routier modèle",
    contactModelButton: "Bouton contact modèle",

    // Vehicle Model CTA Group (buttonFields)
    vehicleModelCTA: "CTA modèle de véhicule",
  },
  validation: {},
  descriptions: {
    // Group descriptions
    brandGridStates: "Messages pour les états vides et conditions de grille",
    vehicleModelContent:
      "Étiquettes de contenu et messages pour les pages de modèles de véhicules",
    vehicleModelTabs:
      "Étiquettes d'onglets et descriptions pour les sections de modèles de véhicules",
    brandQuickActions:
      "Boutons d'action configurables pour les interactions de marque",
    vehicleModelNavigation:
      "Liens de navigation configurables pour les pages de modèles de véhicules",
    vehicleModelQuickActions:
      "Boutons d'action configurables pour les interactions de modèles de véhicules",

    // Individual field descriptions (keeping in English as per convention)
    noBrandsFound: "Message when no brands are found",
    checkBackLater: "Message to check back later for brands",
    noVehiclesFound: "Message when no vehicles are found",
    checkBackForModels: "Message to check back later for models",
    noBrandsAvailable: "Message when no brands are available",
    details: "Uppercase label for details section",
    interestedIn: "Label for interested in prefix text",
    specialistsCanHelp: "Label for specialists can help description text",
    testDrive: "Label for test drive action",
    contact: "Label for contact action",
    logoAltText: "Alt text pattern for logos",
    imageAltText: "Alt text pattern for images",
    overviewTab: "Uppercase label for overview tab",
    techSpecificationTab: "Uppercase label for technical specification tab",
    vehiclesTab: "Uppercase label for vehicles tab",
    galleryTab: "Uppercase label for gallery tab",
    offersTab: "Uppercase label for offers tab",
    configureTab: "Uppercase label for configure tab",
    technicalDetails: "Label for technical details description",
    availableModels: "Label for available models description",
    specialOffers: "Label for special offers description",
    bookTestDriveAction:
      "Link action for booking test drives (collapsible section)",
    findDealerAction: "Link action for finding dealers (collapsible section)",
    contactSalesAction:
      "Link action for contacting sales (collapsible section)",
    getBrochureAction:
      "Link action for getting brochures (collapsible section)",
    testDriveButton: "Button for test drive action (contact section)",
    contactButton: "Button for contact action (contact section)",
    overviewNavigation: "Navigation link to overview page",
    galleryNavigation: "Navigation link to gallery page",
    configureNavigation: "Navigation link to configure page",
    compareNavigation: "Navigation link to compare page",
    bookTestDriveModelAction:
      "Link action for booking test drives from model page (collapsible section)",
    configureModelAction:
      "Link action for configuring vehicle model (collapsible section)",
    compareModelAction:
      "Link action for comparing vehicle models (collapsible section)",
    findDealerModelAction:
      "Link action for finding dealers from model page (collapsible section)",
    contactSalesModelAction:
      "Link action for contacting sales from model page (collapsible section)",
    shareVehicleAction:
      "Link action for sharing vehicle information (collapsible section)",
    testDriveModelButton:
      "Button for test drive action from model page (contact section)",
    contactModelButton:
      "Button for contact action from model page (contact section)",
    vehicleModelCTA: "Appel à l'action du modèle de véhicule",
  },
} as const;
