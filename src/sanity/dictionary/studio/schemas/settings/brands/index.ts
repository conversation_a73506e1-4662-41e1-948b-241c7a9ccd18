import * as en from "./en";
import * as fr from "./fr";
import { setupSchemaTranslations } from "../../utils";

const { dict, createField, getTranslations } = setupSchemaTranslations({
  en: en.brandsSettingsTranslations,
  fr: fr.brandsSettingsTranslations,
});

export const brandsSettingsDict = dict;
export const createBrandsSettingsField = createField;
export const getBrandsSettingsTranslations = getTranslations;
export type BrandsSettingsTranslations = typeof en.brandsSettingsTranslations;
