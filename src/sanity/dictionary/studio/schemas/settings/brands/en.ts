// English translations for marketing settings schema
export const brandsSettingsTranslations = {
  document: {
    name: "brandsSettings",
    title: "Brands Settings",
    description: "Brands page configuration",
  },
  fields: {
    // Brand Grid States Group (SIMPLE LABELS)
    brandGridStates: "Brand Grid States",
    noBrandsFound: "No Brands Found",
    checkBackLater: "Check back later for new brands.",
    noVehiclesFound: "No Vehicles Found",
    checkBackForModels: "Check back later for new models.",
    noBrandsAvailable: "No Brands Available",

    // Brand Quick Actions Group (MIXED: linkFields + buttonFields)
    brandQuickActions: "Brand Quick Actions",
    bookTestDriveAction: "Book Test Drive Action",
    findDealerAction: "Find Dealer Action",
    contactSalesAction: "Contact Sales Action",
    getBrochureAction: "Get Brochure Action",

    // Vehicle Model Content Group (SIMPLE LABELS)
    vehicleModelContent: "Vehicle Model Content",
    details: "DETAILS",
    interestedIn: "Interested in this",
    specialistsCanHelp:
      "specialists can help you configure, finance, and schedule a test drive.",
    testDrive: "Test Drive",
    contact: "Contact",
    logoAltText: "logo",
    imageAltText: "image",

    // Vehicle Model Tabs Group (SIMPLE LABELS)
    vehicleModelTabs: "Vehicle Model Tabs",
    overviewTab: "OVERVIEW",
    overviewTabDescription: "Vehicle details",
    techSpecificationTab: "TECH SPECIFICATION",
    techSpecificationTabDescription: "Technical details",
    vehiclesTab: "VEHICLES",
    vehiclesTabDescription: "Available models",
    galleryTab: "GALLERY",
    galleryTabDescription: "Photos & videos",
    offersTab: "OFFERS",
    offersTabDescription: "Special offers & promotions",
    configureTab: "CONFIGURE",
    configureTabDescription: "Build & price",

    // Vehicle Model Navigation Group (linkFields)
    vehicleModelNavigation: "Vehicle Model Navigation",
    overviewNavigation: "Overview Navigation",
    galleryNavigation: "Gallery Navigation",
    configureNavigation: "Configure Navigation",
    compareNavigation: "Compare Navigation",

    // Vehicle Model Quick Actions Group (MIXED: linkFields + buttonFields)
    vehicleModelQuickActions: "Vehicle Model Quick Actions",
    bookTestDriveModelAction: "Book Test Drive Model Action",
    configureModelAction: "Configure Model Action",
    compareModelAction: "Compare Model Action",
    findDealerModelAction: "Find Dealer Model Action",
    contactSalesModelAction: "Contact Sales Model Action",
    shareVehicleAction: "Share Vehicle Action",
    testDriveModelButton: "Test Drive Model Button",
    contactModelButton: "Contact Model Button",

    // Vehicle Model CTA Group (buttonFields)
    vehicleModelCTA: "Vehicle Model CTA",
  },
  validation: {},
  descriptions: {
    // Group descriptions
    brandGridStates: "Messages for empty states and grid conditions",
    vehicleModelContent: "Content labels and messages for vehicle model pages",
    vehicleModelTabs: "Tab labels and descriptions for vehicle model sections",
    brandQuickActions: "Configurable action buttons for brand interactions",
    vehicleModelNavigation:
      "Configurable navigation links for vehicle model pages",
    vehicleModelQuickActions:
      "Configurable action buttons for vehicle model interactions",

    // Individual field descriptions
    noBrandsFound: "Message when no brands are found",
    checkBackLater: "Message to check back later for brands",
    noVehiclesFound: "Message when no vehicles are found",
    checkBackForModels: "Message to check back later for models",
    noBrandsAvailable: "Message when no brands are available",
    details: "Uppercase label for details section",
    interestedIn: "Label for interested in prefix text",
    specialistsCanHelp: "Label for specialists can help description text",
    testDrive: "Label for test drive action",
    contact: "Label for contact action",
    logoAltText: "Alt text pattern for logos",
    imageAltText: "Alt text pattern for images",
    overviewTab: "Uppercase label for overview tab",
    techSpecificationTab: "Uppercase label for technical specification tab",
    vehiclesTab: "Uppercase label for vehicles tab",
    galleryTab: "Uppercase label for gallery tab",
    offersTab: "Uppercase label for offers tab",
    configureTab: "Uppercase label for configure tab",
    technicalDetails: "Label for technical details description",
    availableModels: "Label for available models description",
    specialOffers: "Label for special offers description",
    bookTestDriveAction:
      "Link action for booking test drives (collapsible section)",
    findDealerAction: "Link action for finding dealers (collapsible section)",
    contactSalesAction:
      "Link action for contacting sales (collapsible section)",
    getBrochureAction:
      "Link action for getting brochures (collapsible section)",
    testDriveButton: "Button for test drive action (contact section)",
    contactButton: "Button for contact action (contact section)",
    overviewNavigation: "Navigation link to overview page",
    galleryNavigation: "Navigation link to gallery page",
    configureNavigation: "Navigation link to configure page",
    compareNavigation: "Navigation link to compare page",
    bookTestDriveModelAction:
      "Link action for booking test drives from model page (collapsible section)",
    configureModelAction:
      "Link action for configuring vehicle model (collapsible section)",
    compareModelAction:
      "Link action for comparing vehicle models (collapsible section)",
    findDealerModelAction:
      "Link action for finding dealers from model page (collapsible section)",
    contactSalesModelAction:
      "Link action for contacting sales from model page (collapsible section)",
    shareVehicleAction:
      "Link action for sharing vehicle information (collapsible section)",
    testDriveModelButton:
      "Button for test drive action from model page (contact section)",
    contactModelButton:
      "Button for contact action from model page (contact section)",
    vehicleModelCTA: "Call to action for vehicle model",
  },
} as const;
