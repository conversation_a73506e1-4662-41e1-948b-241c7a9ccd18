{"name": "autocorner-website", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "generate:routes": "next-typesafe-url --outputPath src/utils/routing/_next-typesafe-url_.d.ts", "build": "next build", "start": "next start", "prettier": "prettier --write .", "lint": "eslint .", "lint:tsc": "tsc --noEmit --skipL<PERSON><PERSON><PERSON><PERSON>", "lint:tsc:debug": "tsc --noEmit --skipLib<PERSON>heck --project tsconfig.debug.json", "lint:tsc:trace": "tsc --noEmit --skipLibCheck --generateTrace trace_output", "lint:fix": "eslint src --fix --ext .ts,.tsx", "lint:circular": "dpdm --circular --no-tree --no-warning --tsconfig tsconfig.json \"./**/*.{ts,tsx}\"", "login": "sanity login", "schema": "sanity schema extract", "typegen": "sanity typegen generate", "deploy": "sanity deploy", "schema-deploy": "sanity schema deploy", "migration-create": "sanity migration create", "migration-apply": "sanity migration run --all --no-dry-run", "manifest-extract": "sanity manifest extract --path public/studio/static", "generate:api": "openapi-typescript src/lib/api/autoscout/specs/openapi.yaml -o src/lib/api/autoscout/types/api.ts", "generate:types": "typed-openapi \"src/lib/api/autoscout/specs/openapi.yaml\" -o \"src/lib/api/autoscout/types/generated.ts\"", "generate:schemas": "typed-openapi \"src/lib/api/autoscout/specs/openapi.yaml\" --runtime zod -o \"src/lib/api/autoscout/schemas/generated.ts\""}, "dependencies": {"@hookform/resolvers": "^3.10.0", "@mux/playback-core": "^0.30.1", "@next/env": "^15.5.0", "@next/third-parties": "^15.5.0", "@portabletext/react": "^3.2.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@sanity/image-url": "^1.1.0", "@sanity/locale-fr-fr": "^1.2.23", "@sanity/orderable-document-list": "^1.4.0", "@sanity/types": "^4.5.0", "@sanity/ui": "^3.0.7", "@sanity/vision": "^4.5.0", "@sanity/visual-editing": "^3.0.3", "@tanstack/query-async-storage-persister": "^5.85.5", "@tanstack/react-query": "^5.85.5", "@tanstack/react-query-devtools": "^5.85.5", "@tanstack/react-query-next-experimental": "^5.85.5", "@tanstack/react-table": "^8.21.3", "@trpc/client": "^11.5.0", "@trpc/next": "^11.5.0", "@trpc/server": "^11.5.0", "@trpc/tanstack-react-query": "^11.5.0", "@vercel/analytics": "^1.5.0", "class-variance-authority": "^0.7.1", "client-only": "^0.0.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "embla-carousel": "^8.6.0", "embla-carousel-autoplay": "^8.6.0", "embla-carousel-fade": "^8.6.0", "embla-carousel-react": "^8.6.0", "lucide-react": "^0.540.0", "motion": "^12.23.12", "next": "^15.5.0", "next-sanity": "^10.0.13", "next-themes": "^0.4.6", "next-typesafe-url": "^5.1.8", "next-video": "^2.2.3", "openapi-fetch": "^0.14.0", "openapi-typescript": "^7.9.1", "react": "19.1.1", "react-dom": "19.1.1", "react-hook-form": "^7.62.0", "react-hot-toast": "^2.6.0", "react-icons": "^5.5.0", "react-player": "^3.3.1", "react-redux": "^9.2.0", "react-use-measure": "^2.1.7", "redux": "^5.0.1", "resend": "^6.0.1", "sanity": "^4.5.0", "sanity-plugin-iframe-pane": "^4.0.0", "sanity-plugin-media": "^4.0.0", "sanity-plugin-mux-input": "^2.9.0", "sanity-plugin-simpler-color-input": "^3.1.1", "server-only": "^0.0.1", "styled-components": "^6.1.19", "superjson": "^2.2.2", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "yaml": "^2.8.1", "zod": "^4.0.17"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.33.0", "@tailwindcss/postcss": "^4.1.12", "@tailwindcss/typography": "^0.5.16", "@types/node": "^22.17.2", "@types/react": "19.1.10", "@types/react-dom": "19.1.7", "dpdm": "^3.14.0", "eslint": "^9.34.0", "eslint-config-next": "^15.5.0", "eslint-plugin-react-hooks": "^5.2.0", "postcss": "^8.5.6", "prettier": "^3.6.2", "tailwindcss": "^4.1.12", "tsx": "^4.20.4", "typescript": "^5"}, "volta": {"node": "22.18.0", "pnpm": "10.15.0"}, "overrides": {"@types/react": "19.1.8", "@types/react-dom": "19.1.6"}}